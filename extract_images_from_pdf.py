import fitz  # pip install pymupdf
import os
import io
from PIL import Image

Image.MAX_IMAGE_PIXELS = 300_000_000

def resize_image(image, target_width, target_height, resize_mode='fit'):
    """
    Resize image to fixed dimensions with different modes.

    Parameters:
    - image: PIL Image object
    - target_width: Target width in pixels
    - target_height: Target height in pixels
    - resize_mode: Resize mode ('fit', 'fill', 'stretch')
        - 'fit': Maintain aspect ratio, fit within target size (may have padding)
        - 'fill': Maintain aspect ratio, fill target size (may crop)
        - 'stretch': Ignore aspect ratio, stretch to exact size

    Returns:
    - Resized PIL Image object
    """
    original_width, original_height = image.size

    if resize_mode == 'stretch':
        # Simply stretch to target dimensions
        return image.resize((target_width, target_height), Image.Resampling.LANCZOS)

    elif resize_mode == 'fit':
        # Maintain aspect ratio, fit within target size
        # Calculate scaling factor to fit within bounds
        scale_w = target_width / original_width
        scale_h = target_height / original_height
        scale = min(scale_w, scale_h)

        # Calculate new dimensions
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)

        # Resize image
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Create new image with target size and paste resized image in center
        final_image = Image.new('RGB', (target_width, target_height), (255, 255, 255))
        paste_x = (target_width - new_width) // 2
        paste_y = (target_height - new_height) // 2
        final_image.paste(resized_image, (paste_x, paste_y))

        return final_image

    elif resize_mode == 'fill':
        # Maintain aspect ratio, fill target size (may crop)
        # Calculate scaling factor to fill the target size
        scale_w = target_width / original_width
        scale_h = target_height / original_height
        scale = max(scale_w, scale_h)

        # Calculate new dimensions
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)

        # Resize image
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Crop to target size from center
        crop_x = (new_width - target_width) // 2
        crop_y = (new_height - target_height) // 2
        final_image = resized_image.crop((
            crop_x, crop_y,
            crop_x + target_width,
            crop_y + target_height
        ))

        return final_image

    else:
        raise ValueError("resize_mode must be 'fit', 'fill', or 'stretch'")


def get_images(pdf_path, output_dir, target_width=1024, target_height=1024, resize_mode='fit', dpi=300):
    """
    Extract images from PDF and save them with fixed dimensions.

    Parameters:
    - pdf_path: Path to the PDF file
    - output_dir: Directory to save extracted images
    - target_width: Target width for output images (default: 1024)
    - target_height: Target height for output images (default: 1024)
    - resize_mode: How to resize images ('fit', 'fill', 'stretch')
    - dpi: DPI for PDF rendering (default: 300)
    """
    doc = fitz.open(pdf_path)
    os.makedirs(output_dir, exist_ok=True)
    imgs = []

    for i, page in enumerate(doc):
        pix = page.get_pixmap(dpi=dpi)
        image = Image.open(io.BytesIO(pix.tobytes("png")))

        # Resize image to fixed dimensions
        resized_image = resize_image(image, target_width, target_height, resize_mode)
        imgs.append(resized_image)

        # Save resized image
        resized_image.save(f"{output_dir}/page_{i+1}.png")

    print(f"Extracted {len(doc)} images from {pdf_path}")
    print(f"Images resized to {target_width}x{target_height} using '{resize_mode}' mode")
    doc.close()

    return imgs


if __name__ == "__main__":
    print("Extracting images from PDFs...")
    get_images("1502.03167v3.pdf", "extracted_images/1502.03167v3.pdf")