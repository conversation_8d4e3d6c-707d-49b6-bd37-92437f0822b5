import layoutparser as lp
import cv2
import os

def draw_layout_boxes(image, layout, box_width=2, with_labels=True):
    """
    Custom function to draw layout boxes on an image.

    Parameters:
    - image: The original image (as NumPy array)
    - layout: A layoutparser Layout object
    - box_width: Thickness of the bounding box lines
    - with_labels: Whether to draw type labels on boxes
    """
    image_copy = image.copy()

    for block in layout:
        x1, y1, x2, y2 = map(int, (block.block.x_1, block.block.y_1, block.block.x_2, block.block.y_2))
        label = block.type if block.type else "Block"

        # Choose color by type (optional)
        if label.lower() == "text":
            color = (0, 255, 0)  # Green
        elif label.lower() == "figure":
            color = (0, 0, 255)  # Red
        else:
            color = (255, 0, 0)  # Blue

        # Draw rectangle
        cv2.rectangle(image_copy, (x1, y1), (x2, y2), color, box_width)

        # Put label text
        if with_labels:
            cv2.putText(image_copy, label, (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    return image_copy



def extract_layout(image_path, output_path):
    model = lp.Detectron2LayoutModel(
        config_path='lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
        extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
        label_map={0: "Text", 1: "Title", 2: "List", 3:"Table", 4:"Figure"},
        device="cpu"
        )
    image = cv2.imread(image_path)
    image = image[..., ::-1]
    layout = model.detect(image)
    figure_blocks = lp.Layout([b for b in layout if b.type and b.type.lower() == "figure"])

    output_image = draw_layout_boxes(image, fig, box_width=2, with_labels=True)

    # Save output to file
    cv2.imwrite(output_path, cv2.cvtColor(output_image, cv2.COLOR_RGB2BGR))
    print(f"[✅] Layout image saved as {output_path}")


input_dir = "extracted_images/1502.03167v3.pdf"
output_dir = "layout_images"

os.makedirs(output_dir, exist_ok=True)

start_index, end_index = 1, 11

for i in range(start_index, end_index + 1):
    extract_layout(f"{input_dir}/page_{i}.png", f"{output_dir}/page_{i}.png")